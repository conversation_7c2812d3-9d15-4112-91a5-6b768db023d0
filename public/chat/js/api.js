/**
 * 当贝AI聊天界面 - API客户端
 * 封装HTTP API调用，处理聊天和模型相关的请求
 * 实现当贝API的认证机制和正确的调用流程
 */

class ApiClient {
  constructor() {
    // 当贝API的基础配置
    this.apiBaseUrl = 'https://ai-api.dangbei.net';
    this.timeout = 30000; // 30秒超时
    this.maxRetries = 3; // 最大重试次数
    this.retryDelay = 1000; // 重试延迟（毫秒）

    // 当贝API必需的headers配置
    this.defaultHeaders = {
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
      'Connection': 'keep-alive',
      'Origin': 'https://ai.dangbei.com',
      'Referer': 'https://ai.dangbei.com/',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0',
      'appType': '6',
      'appVersion': '1.1.17-22',
      'client-ver': '1.0.2',
      'content-type': 'application/json',
      'lang': 'zh',
      'sec-ch-ua': '"Not;A=Brand";v="99", "Microsoft Edge";v="139", "Chromium";v="139"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'token': ''
    };

    // 设备ID - 在实际应用中应该从本地存储获取或生成
    this.deviceId = this.getOrGenerateDeviceId();

    // 当前的EventSource连接
    this.currentEventSource = null;
  }

  /**
   * 获取或生成设备ID
   * @returns {string} 设备ID
   */
  getOrGenerateDeviceId() {
    let deviceId = localStorage.getItem('dangbei_device_id');
    if (!deviceId) {
      // 生成类似当贝格式的设备ID
      const randomPart = Math.random().toString(36).substring(2, 15);
      const timestamp = Date.now().toString(36);
      deviceId = `${randomPart}_${timestamp}`;
      localStorage.setItem('dangbei_device_id', deviceId);
    }
    return deviceId;
  }

  /**
   * 生成随机nonce字符串
   * @param {number} length - 字符串长度
   * @returns {string} 随机字符串
   */
  generateNonce(length = 17) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成MD5签名（使用简单的MD5实现）
   * @param {string} data - 要签名的数据
   * @returns {string} MD5签名（大写）
   */
  generateMD5(data) {
    // 简单的MD5实现，用于当贝API签名
    // 注意：这是一个简化版本，实际项目中建议使用专业的MD5库
    function md5(string) {
      function md5_RotateLeft(lValue, iShiftBits) {
        return (lValue<<iShiftBits) | (lValue>>>(32-iShiftBits));
      }
      function md5_AddUnsigned(lX,lY) {
        var lX4,lY4,lX8,lY8,lResult;
        lX8 = (lX & 0x80000000);
        lY8 = (lY & 0x80000000);
        lX4 = (lX & 0x40000000);
        lY4 = (lY & 0x40000000);
        lResult = (lX & 0x3FFFFFFF)+(lY & 0x3FFFFFFF);
        if (lX4 & lY4) {
          return (lResult ^ 0x80000000 ^ lX8 ^ lY8);
        }
        if (lX4 | lY4) {
          if (lResult & 0x40000000) {
            return (lResult ^ 0xC0000000 ^ lX8 ^ lY8);
          } else {
            return (lResult ^ 0x40000000 ^ lX8 ^ lY8);
          }
        } else {
          return (lResult ^ lX8 ^ lY8);
        }
      }
      function md5_F(x,y,z) { return (x & y) | ((~x) & z); }
      function md5_G(x,y,z) { return (x & z) | (y & (~z)); }
      function md5_H(x,y,z) { return (x ^ y ^ z); }
      function md5_I(x,y,z) { return (y ^ (x | (~z))); }
      function md5_FF(a,b,c,d,x,s,ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_F(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
      }
      function md5_GG(a,b,c,d,x,s,ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_G(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
      }
      function md5_HH(a,b,c,d,x,s,ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_H(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
      }
      function md5_II(a,b,c,d,x,s,ac) {
        a = md5_AddUnsigned(a, md5_AddUnsigned(md5_AddUnsigned(md5_I(b, c, d), x), ac));
        return md5_AddUnsigned(md5_RotateLeft(a, s), b);
      }
      function md5_ConvertToWordArray(string) {
        var lWordCount;
        var lMessageLength = string.length;
        var lNumberOfWords_temp1=lMessageLength + 8;
        var lNumberOfWords_temp2=(lNumberOfWords_temp1-(lNumberOfWords_temp1 % 64))/64;
        var lNumberOfWords = (lNumberOfWords_temp2+1)*16;
        var lWordArray=Array(lNumberOfWords-1);
        var lBytePosition = 0;
        var lByteCount = 0;
        while ( lByteCount < lMessageLength ) {
          lWordCount = (lByteCount-(lByteCount % 4))/4;
          lBytePosition = (lByteCount % 4)*8;
          lWordArray[lWordCount] = (lWordArray[lWordCount] | (string.charCodeAt(lByteCount)<<lBytePosition));
          lByteCount++;
        }
        lWordCount = (lByteCount-(lByteCount % 4))/4;
        lBytePosition = (lByteCount % 4)*8;
        lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80<<lBytePosition);
        lWordArray[lNumberOfWords-2] = lMessageLength<<3;
        lWordArray[lNumberOfWords-1] = lMessageLength>>>29;
        return lWordArray;
      }
      function md5_WordToHex(lValue) {
        var WordToHexValue="",WordToHexValue_temp="",lByte,lCount;
        for (lCount = 0;lCount<=3;lCount++) {
          lByte = (lValue>>>(lCount*8)) & 255;
          WordToHexValue_temp = "0" + lByte.toString(16);
          WordToHexValue = WordToHexValue + WordToHexValue_temp.substr(WordToHexValue_temp.length-2,2);
        }
        return WordToHexValue;
      }
      var x=Array();
      var k,AA,BB,CC,DD,a,b,c,d;
      var S11=7, S12=12, S13=17, S14=22;
      var S21=5, S22=9 , S23=14, S24=20;
      var S31=4, S32=11, S33=16, S34=23;
      var S41=6, S42=10, S43=15, S44=21;
      string = string.replace(/\r\n/g,"\n");
      x = md5_ConvertToWordArray(string);
      a = 0x67452301; b = 0xEFCDAB89; c = 0x98BADCFE; d = 0x10325476;
      for (k=0;k<x.length;k+=16) {
        AA=a; BB=b; CC=c; DD=d;
        a=md5_FF(a,b,c,d,x[k+0], S11,0xD76AA478);
        d=md5_FF(d,a,b,c,x[k+1], S12,0xE8C7B756);
        c=md5_FF(c,d,a,b,x[k+2], S13,0x242070DB);
        b=md5_FF(b,c,d,a,x[k+3], S14,0xC1BDCEEE);
        a=md5_FF(a,b,c,d,x[k+4], S11,0xF57C0FAF);
        d=md5_FF(d,a,b,c,x[k+5], S12,0x4787C62A);
        c=md5_FF(c,d,a,b,x[k+6], S13,0xA8304613);
        b=md5_FF(b,c,d,a,x[k+7], S14,0xFD469501);
        a=md5_FF(a,b,c,d,x[k+8], S11,0x698098D8);
        d=md5_FF(d,a,b,c,x[k+9], S12,0x8B44F7AF);
        c=md5_FF(c,d,a,b,x[k+10],S13,0xFFFF5BB1);
        b=md5_FF(b,c,d,a,x[k+11],S14,0x895CD7BE);
        a=md5_FF(a,b,c,d,x[k+12],S11,0x6B901122);
        d=md5_FF(d,a,b,c,x[k+13],S12,0xFD987193);
        c=md5_FF(c,d,a,b,x[k+14],S13,0xA679438E);
        b=md5_FF(b,c,d,a,x[k+15],S14,0x49B40821);
        a=md5_GG(a,b,c,d,x[k+1], S21,0xF61E2562);
        d=md5_GG(d,a,b,c,x[k+6], S22,0xC040B340);
        c=md5_GG(c,d,a,b,x[k+11],S23,0x265E5A51);
        b=md5_GG(b,c,d,a,x[k+0], S24,0xE9B6C7AA);
        a=md5_GG(a,b,c,d,x[k+5], S21,0xD62F105D);
        d=md5_GG(d,a,b,c,x[k+10],S22,0x2441453);
        c=md5_GG(c,d,a,b,x[k+15],S23,0xD8A1E681);
        b=md5_GG(b,c,d,a,x[k+4], S24,0xE7D3FBC8);
        a=md5_GG(a,b,c,d,x[k+9], S21,0x21E1CDE6);
        d=md5_GG(d,a,b,c,x[k+14],S22,0xC33707D6);
        c=md5_GG(c,d,a,b,x[k+3], S23,0xF4D50D87);
        b=md5_GG(b,c,d,a,x[k+8], S24,0x455A14ED);
        a=md5_GG(a,b,c,d,x[k+13],S21,0xA9E3E905);
        d=md5_GG(d,a,b,c,x[k+2], S22,0xFCEFA3F8);
        c=md5_GG(c,d,a,b,x[k+7], S23,0x676F02D9);
        b=md5_GG(b,c,d,a,x[k+12],S24,0x8D2A4C8A);
        a=md5_HH(a,b,c,d,x[k+5], S31,0xFFFA3942);
        d=md5_HH(d,a,b,c,x[k+8], S32,0x8771F681);
        c=md5_HH(c,d,a,b,x[k+11],S33,0x6D9D6122);
        b=md5_HH(b,c,d,a,x[k+14],S34,0xFDE5380C);
        a=md5_HH(a,b,c,d,x[k+1], S31,0xA4BEEA44);
        d=md5_HH(d,a,b,c,x[k+4], S32,0x4BDECFA9);
        c=md5_HH(c,d,a,b,x[k+7], S33,0xF6BB4B60);
        b=md5_HH(b,c,d,a,x[k+10],S34,0xBEBFBC70);
        a=md5_HH(a,b,c,d,x[k+13],S31,0x289B7EC6);
        d=md5_HH(d,a,b,c,x[k+0], S32,0xEAA127FA);
        c=md5_HH(c,d,a,b,x[k+3], S33,0xD4EF3085);
        b=md5_HH(b,c,d,a,x[k+6], S34,0x4881D05);
        a=md5_HH(a,b,c,d,x[k+9], S31,0xD9D4D039);
        d=md5_HH(d,a,b,c,x[k+12],S32,0xE6DB99E5);
        c=md5_HH(c,d,a,b,x[k+15],S33,0x1FA27CF8);
        b=md5_HH(b,c,d,a,x[k+2], S34,0xC4AC5665);
        a=md5_II(a,b,c,d,x[k+0], S41,0xF4292244);
        d=md5_II(d,a,b,c,x[k+7], S42,0x432AFF97);
        c=md5_II(c,d,a,b,x[k+14],S43,0xAB9423A7);
        b=md5_II(b,c,d,a,x[k+5], S44,0xFC93A039);
        a=md5_II(a,b,c,d,x[k+12],S41,0x655B59C3);
        d=md5_II(d,a,b,c,x[k+3], S42,0x8F0CCC92);
        c=md5_II(c,d,a,b,x[k+10],S43,0xFFEFF47D);
        b=md5_II(b,c,d,a,x[k+1], S44,0x85845DD1);
        a=md5_II(a,b,c,d,x[k+8], S41,0x6FA87E4F);
        d=md5_II(d,a,b,c,x[k+15],S42,0xFE2CE6E0);
        c=md5_II(c,d,a,b,x[k+6], S43,0xA3014314);
        b=md5_II(b,c,d,a,x[k+13],S44,0x4E0811A1);
        a=md5_II(a,b,c,d,x[k+4], S41,0xF7537E82);
        d=md5_II(d,a,b,c,x[k+11],S42,0xBD3AF235);
        c=md5_II(c,d,a,b,x[k+2], S43,0x2AD7D2BB);
        b=md5_II(b,c,d,a,x[k+9], S44,0xEB86D391);
        a=md5_AddUnsigned(a,AA);
        b=md5_AddUnsigned(b,BB);
        c=md5_AddUnsigned(c,CC);
        d=md5_AddUnsigned(d,DD);
      }
      return (md5_WordToHex(a)+md5_WordToHex(b)+md5_WordToHex(c)+md5_WordToHex(d)).toLowerCase();
    }
    return md5(data).toUpperCase();
  }

  /**
   * 生成API签名
   * @param {string} method - HTTP方法
   * @param {string} path - API路径
   * @param {number} timestamp - 时间戳（秒）
   * @param {string} nonce - 随机字符串
   * @returns {string} 签名
   */
  generateSign(method, path, timestamp, nonce) {
    // 规范化串格式：${METHOD} ${PATH}
    const normalizedString = `${method.toUpperCase()} ${path}`;
    // 签名数据：${timestamp}${normalizedString}${nonce}
    const signData = `${timestamp}${normalizedString}${nonce}`;
    return this.generateMD5(signData);
  }

  /**
   * 生成带认证的headers
   * @param {string} method - HTTP方法
   * @param {string} path - API路径
   * @returns {Object} 包含认证信息的headers
   */
  generateAuthHeaders(method, path) {
    const timestamp = Math.floor(Date.now() / 1000); // 秒级时间戳
    const nonce = this.generateNonce();
    const sign = this.generateSign(method, path, timestamp, nonce);

    return {
      ...this.defaultHeaders,
      'deviceId': this.deviceId,
      'timestamp': timestamp.toString(),
      'nonce': nonce,
      'sign': sign
    };
  }

  /**
   * 发送HTTP请求到当贝API
   * @param {string} path - API路径
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async request(path, options = {}) {
    const method = options.method || 'GET';
    const headers = this.generateAuthHeaders(method, path);

    const config = {
      method,
      headers: {
        ...headers,
        ...options.headers
      },
      ...options
    };

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    try {
      const response = await fetch(`${this.apiBaseUrl}${path}`, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return await response.text();
      }
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }

      throw error;
    }
  }

  /**
   * 创建对话会话
   * @param {Object} options - 创建选项
   * @returns {Promise<Object>} 对话会话信息
   */
  async createConversation(options = {}) {
    const path = '/ai-search/conversationApi/v1/batch/create';

    const requestBody = {
      conversationList: [{
        metaData: {
          chatModelConfig: options.chatModelConfig || {},
          superAgentPath: "/chat"
        },
        shareId: "",
        isAnonymous: false,
        source: ""
      }]
    };

    try {
      console.log('创建对话会话:', requestBody);
      const response = await this.request(path, {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      if (response.success && response.data) {
        console.log('对话会话创建成功:', response.data);
        return response.data;
      } else {
        throw new Error(response.errMessage || '创建对话会话失败');
      }
    } catch (error) {
      console.error('创建对话会话失败:', error);
      throw error;
    }
  }

  /**
   * 生成消息ID
   * @returns {Promise<string>} 消息ID
   */
  async generateId() {
    const path = '/ai-search/commonApi/v1/generateId';
    const timestamp = Date.now();

    const requestBody = {
      timestamp: timestamp
    };

    try {
      console.log('生成消息ID:', requestBody);
      const response = await this.request(path, {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      if (response.success && response.data) {
        console.log('消息ID生成成功:', response.data);
        return response.data;
      } else {
        throw new Error(response.errMessage || '生成消息ID失败');
      }
    } catch (error) {
      console.error('生成消息ID失败:', error);
      throw error;
    }
  }

  /**
   * 发送流式聊天消息
   * @param {Object} params - 聊天参数
   * @param {Function} onMessage - 消息回调
   * @param {Function} onComplete - 完成回调
   * @param {Function} onError - 错误回调
   */
  async sendStreamMessage(params, onMessage, onComplete, onError) {
    const {
      conversationId,
      question,
      model = 'doubao-1_6-thinking',
      chatOption = {
        searchKnowledge: false,
        searchAllKnowledge: false,
        searchSharedKnowledge: false
      },
      userAction = 'deep,online'
    } = params;

    // 生成消息ID
    let messageId;
    try {
      messageId = await this.generateId();
    } catch (error) {
      console.error('生成消息ID失败:', error);
      onError(error);
      return;
    }

    const path = '/ai-search/chatApi/v2/chat';
    const requestBody = {
      stream: true,
      botCode: "AI_SEARCH",
      conversationId: conversationId,
      question: question,
      model: model,
      chatOption: chatOption,
      knowledgeList: [],
      anonymousKey: "",
      uuid: messageId,
      chatId: messageId,
      files: [],
      reference: [],
      role: "user",
      status: "local",
      content: question,
      userAction: userAction,
      agentId: ""
    };

    try {
      console.log('发送流式聊天消息:', requestBody);

      // 生成认证headers
      const headers = this.generateAuthHeaders('POST', path);

      // 发送请求
      const response = await fetch(`${this.apiBaseUrl}${path}`, {
        method: 'POST',
        headers: {
          ...headers,
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 处理SSE流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            console.log('SSE流结束');
            break;
          }

          // 解码数据
          buffer += decoder.decode(value, { stream: true });

          // 处理完整的SSE事件
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留不完整的行

          for (const line of lines) {
            if (line.trim() === '') continue;

            // 解析SSE数据
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();

              if (data === '[DONE]') {
                console.log('SSE数据流结束');
                onComplete();
                return;
              }

              try {
                const parsed = JSON.parse(data);
                console.log('收到SSE消息:', parsed);

                // 处理不同类型的消息
                if (parsed.content_type === 'text') {
                  // 正常文本消息
                  onMessage({
                    type: 'text',
                    content: parsed.content,
                    messageId: parsed.id,
                    conversationId: parsed.conversation_id
                  });
                } else if (parsed.content_type === 'thinking') {
                  // 思考过程消息
                  onMessage({
                    type: 'thinking',
                    content: parsed.content,
                    messageId: parsed.id,
                    conversationId: parsed.conversation_id
                  });
                } else if (parsed.content_type === 'progress') {
                  // 进度消息（如联网搜索）
                  onMessage({
                    type: 'progress',
                    content: parsed.content,
                    messageId: parsed.id,
                    conversationId: parsed.conversation_id
                  });
                } else if (parsed.content_type === 'card') {
                  // 卡片消息（如搜索结果）
                  onMessage({
                    type: 'card',
                    content: parsed.content,
                    messageId: parsed.id,
                    conversationId: parsed.conversation_id
                  });
                }
              } catch (parseError) {
                console.warn('解析SSE数据失败:', parseError, data);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error('流式消息发送失败:', error);
      onError(error);
    }
  }

  /**
   * 获取模型列表（当贝API暂不支持，返回预定义模型）
   * @returns {Promise<Object>} 模型列表数据
   */
  async getModels() {
    try {
      // 当贝API暂不提供模型列表接口，返回预定义的模型列表
      const models = [
        {
          id: 'doubao-1_6-thinking',
          name: '豆包思考版',
          description: '支持深度思考的智能对话模型',
          provider: '字节跳动',
          capabilities: ['chat', 'thinking', 'search'],
          maxTokens: 4000,
          isDefault: true
        },
        {
          id: 'doubao-pro',
          name: '豆包专业版',
          description: '高性能的专业对话模型',
          provider: '字节跳动',
          capabilities: ['chat', 'search'],
          maxTokens: 8000,
          isDefault: false
        }
      ];

      console.log('返回预定义模型列表:', models);

      return {
        defaultModel: 'doubao-1_6-thinking',
        models: models,
        total: models.length
      };
    } catch (error) {
      console.error('获取模型列表失败:', error);
      throw new Error(`获取模型列表失败: ${error.message}`);
    }
  }

  /**
   * 获取特定模型信息
   * @param {string} modelId - 模型ID
   * @returns {Promise<Object>} 模型信息
   */
  async getModelInfo(modelId) {
    try {
      // 从预定义模型列表中查找
      const modelsData = await this.getModels();
      const model = modelsData.models.find(m => m.id === modelId);

      if (model) {
        console.log('找到模型信息:', model);
        return model;
      } else {
        throw new Error(`模型 ${modelId} 不存在`);
      }
    } catch (error) {
      console.error('获取模型信息失败:', error);
      throw new Error(`获取模型信息失败: ${error.message}`);
    }
  }

  /**
   * 关闭当前的流式连接
   */
  closeStream() {
    if (this.currentEventSource) {
      this.currentEventSource.close();
      this.currentEventSource = null;
    }
  }

  /**
   * 关闭当前的流式连接
   */
  closeStream() {
    if (this.currentEventSource) {
      this.currentEventSource.close();
      this.currentEventSource = null;
    }
  }

  /**
   * 测试网络连接（简单的连通性测试）
   * @returns {Promise<boolean>} 连接状态
   */
  async testConnection() {
    try {
      // 尝试访问当贝API的基础URL
      await fetch(this.apiBaseUrl, {
        method: 'HEAD',
        mode: 'no-cors'
      });
      return true;
    } catch (error) {
      console.warn('网络连接测试失败:', error);
      return false;
    }
  }

  /**
   * 将前端选项转换为当贝API期望的格式
   * @param {Object} options - 前端选项
   * @returns {Object} 当贝API格式的选项
   */
  transformOptionsForAPI(options) {
    const chatOption = {
      searchKnowledge: false,
      searchAllKnowledge: false,
      searchSharedKnowledge: false
    };

    // 联网搜索选项
    if (options.online || options.search) {
      chatOption.searchKnowledge = true;
      chatOption.searchAllKnowledge = true;
    }

    return chatOption;
  }

  /**
   * 生成用户操作标识
   * @param {Object} options - 选项
   * @returns {string} 用户操作字符串
   */
  generateUserAction(options) {
    const actions = [];

    if (options.thinking || options.deep) {
      actions.push('deep');
    }

    if (options.online || options.search) {
      actions.push('online');
    }

    return actions.length > 0 ? actions.join(',') : 'normal';
  }

  /**
   * 获取错误信息的用户友好描述
   * @param {Error} error - 错误对象
   * @returns {string} 用户友好的错误描述
   */
  getErrorMessage(error) {
    const message = error.message || '未知错误';

    if (message.includes('fetch')) {
      return '网络连接失败，请检查网络设置';
    }

    if (message.includes('timeout') || message.includes('超时')) {
      return '请求超时，请稍后重试';
    }

    if (message.includes('500')) {
      return '服务器内部错误，请稍后重试';
    }

    if (message.includes('502') || message.includes('503') || message.includes('504')) {
      return '服务暂时不可用，请稍后重试';
    }

    if (message.includes('401')) {
      return '认证失败，请检查权限';
    }

    if (message.includes('403')) {
      return '访问被拒绝，请检查权限';
    }

    if (message.includes('404')) {
      return '请求的资源不存在';
    }

    if (message.includes('429')) {
      return '请求过于频繁，请稍后重试';
    }

    return message;
  }
}

// 创建全局API客户端实例
const apiClient = new ApiClient();
