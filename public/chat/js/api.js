/**
 * 当贝AI聊天界面 - API客户端
 * 封装HTTP API调用，处理聊天和模型相关的请求
 */

class ApiClient {
  constructor() {
    this.baseUrl = '/api';
    this.timeout = 30000; // 30秒超时
    this.maxRetries = 3; // 最大重试次数
    this.retryDelay = 1000; // 重试延迟（毫秒）
    
    // 当前的EventSource连接
    this.currentEventSource = null;
  }

  /**
   * 发送HTTP请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async request(url, options = {}) {
    const config = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    config.signal = controller.signal;

    try {
      const response = await fetch(`${this.baseUrl}${url}`, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return await response.text();
      }
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }
      
      throw error;
    }
  }

  /**
   * 带重试的请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @param {number} retries - 剩余重试次数
   * @returns {Promise} 请求结果
   */
  async requestWithRetry(url, options = {}, retries = this.maxRetries) {
    try {
      return await this.request(url, options);
    } catch (error) {
      if (retries > 0 && this.shouldRetry(error)) {
        console.warn(`请求失败，${this.retryDelay}ms后重试 (剩余${retries}次):`, error.message);
        await sleep(this.retryDelay);
        return this.requestWithRetry(url, options, retries - 1);
      }
      throw error;
    }
  }

  /**
   * 判断是否应该重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(error) {
    // 网络错误或服务器错误可以重试
    return error.message.includes('fetch') || 
           error.message.includes('500') || 
           error.message.includes('502') || 
           error.message.includes('503') || 
           error.message.includes('504');
  }

  /**
   * 获取模型列表
   * @returns {Promise<Object>} 模型列表数据
   */
  async getModels() {
    try {
      const response = await this.requestWithRetry('/models');

      if (response.success) {
        // 处理现有API控制器的响应格式
        const data = response.data;

        // 如果data直接包含models数组，则使用它
        if (data.models) {
          return {
            defaultModel: data.defaultModel,
            models: data.models,
            total: data.total || data.models.length
          };
        }

        // 如果data本身就是models数组，则包装它
        if (Array.isArray(data)) {
          return {
            defaultModel: data.length > 0 ? data[0].id : null,
            models: data,
            total: data.length
          };
        }

        // 其他情况的处理
        return {
          defaultModel: null,
          models: [],
          total: 0
        };
      } else {
        throw new Error(response.error?.message || '获取模型列表失败');
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
      throw new Error(`获取模型列表失败: ${error.message}`);
    }
  }

  /**
   * 获取特定模型信息
   * @param {string} modelId - 模型ID
   * @returns {Promise<Object>} 模型信息
   */
  async getModelInfo(modelId) {
    try {
      const response = await this.requestWithRetry(`/models/${modelId}`);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error?.message || '获取模型信息失败');
      }
    } catch (error) {
      console.error('获取模型信息失败:', error);
      throw new Error(`获取模型信息失败: ${error.message}`);
    }
  }

  /**
   * 发送聊天消息（非流式）
   * @param {Object} params - 聊天参数
   * @returns {Promise<Object>} 聊天响应
   */
  async sendMessage(params) {
    const {
      messages,
      model,
      conversationId,
      options = {}
    } = params;

    try {
      const requestBody = {
        messages,
        model,
        stream: false,
        conversation_id: conversationId,
        // 转换选项格式
        options: this.transformOptionsForAPI(options)
      };

      const response = await this.requestWithRetry('/chat', {
        method: 'POST',
        body: JSON.stringify(requestBody)
      });

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error?.message || '发送消息失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      throw new Error(`发送消息失败: ${error.message}`);
    }
  }

  /**
   * 发送流式聊天消息
   * @param {Object} params - 聊天参数
   * @param {Object} callbacks - 回调函数
   * @returns {Promise<void>}
   */
  async sendStreamMessage(params, callbacks = {}) {
    const {
      messages,
      model,
      conversationId,
      options = {}
    } = params;

    const {
      onMessage = () => {},
      onComplete = () => {},
      onError = () => {}
    } = callbacks;

    // 关闭之前的连接
    this.closeStream();

    try {
      // 根据现有API格式构建请求体
      const requestBody = {
        messages,
        model,
        stream: true,
        conversation_id: conversationId,
        // 将前端选项转换为API期望的格式
        options: this.transformOptionsForAPI(options)
      };

      // 创建EventSource连接
      const eventSourceUrl = `${this.baseUrl}/chat`;
      
      // 由于EventSource不支持POST，我们需要使用fetch的流式响应
      const response = await fetch(eventSourceUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.trim() === '') continue;
            
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              
              if (data === '[DONE]') {
                onComplete();
                return;
              }

              try {
                const parsed = JSON.parse(data);
                
                if (parsed.error) {
                  onError(new Error(parsed.error.message || '流式响应错误'));
                  return;
                }

                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                  const delta = parsed.choices[0].delta;
                  if (delta.content) {
                    onMessage(delta.content, parsed);
                  }
                  
                  if (parsed.choices[0].finish_reason === 'stop') {
                    onComplete(parsed);
                    return;
                  }
                }
              } catch (parseError) {
                console.warn('解析SSE数据失败:', parseError, data);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error('流式消息发送失败:', error);
      onError(error);
    }
  }

  /**
   * 关闭当前的流式连接
   */
  closeStream() {
    if (this.currentEventSource) {
      this.currentEventSource.close();
      this.currentEventSource = null;
    }
  }

  /**
   * 检查API健康状态
   * @returns {Promise<Object>} 健康状态
   */
  async checkHealth() {
    try {
      const response = await this.request('/health');
      return response;
    } catch (error) {
      console.error('健康检查失败:', error);
      throw error;
    }
  }

  /**
   * 获取API信息
   * @returns {Promise<Object>} API信息
   */
  async getApiInfo() {
    try {
      const response = await this.request('/info');
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error('获取API信息失败');
      }
    } catch (error) {
      console.error('获取API信息失败:', error);
      throw error;
    }
  }

  /**
   * 测试网络连接
   * @returns {Promise<boolean>} 连接状态
   */
  async testConnection() {
    try {
      await this.checkHealth();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 将前端选项转换为API期望的格式
   * @param {Object} options - 前端选项
   * @returns {Object} API格式的选项
   */
  transformOptionsForAPI(options) {
    const apiOptions = {};

    // 深度思考选项
    if (options.deep) {
      apiOptions.deep_thinking = true;
    }

    // 联网搜索选项
    if (options.online) {
      apiOptions.online_search = true;
    }

    return apiOptions;
  }

  /**
   * 获取错误信息的用户友好描述
   * @param {Error} error - 错误对象
   * @returns {string} 用户友好的错误描述
   */
  getErrorMessage(error) {
    const message = error.message || '未知错误';

    if (message.includes('fetch')) {
      return '网络连接失败，请检查网络设置';
    }

    if (message.includes('timeout') || message.includes('超时')) {
      return '请求超时，请稍后重试';
    }

    if (message.includes('500')) {
      return '服务器内部错误，请稍后重试';
    }

    if (message.includes('502') || message.includes('503') || message.includes('504')) {
      return '服务暂时不可用，请稍后重试';
    }

    if (message.includes('401')) {
      return '认证失败，请检查权限';
    }

    if (message.includes('403')) {
      return '访问被拒绝，请检查权限';
    }

    if (message.includes('404')) {
      return '请求的资源不存在';
    }

    if (message.includes('429')) {
      return '请求过于频繁，请稍后重试';
    }

    return message;
  }
}

// 创建全局API客户端实例
const apiClient = new ApiClient();
